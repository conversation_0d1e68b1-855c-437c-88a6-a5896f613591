import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  QueryDocumentSnapshot,
  DocumentData,
  addDoc,
  onSnapshot,
  Unsubscribe,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import {
  UserDocument,
  QuizDocument,
  QuizAttemptDocument,
  DailyChallengeDocument,
  COLLECTIONS,
  SUBCOLLECTIONS,
} from '../types/firestore';

export class FirestoreService {
  // User operations
  static async createUser(userId: string, userData: Omit<UserDocument, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    const now = Timestamp.now();
    const userDoc: UserDocument = {
      ...userData,
      id: userId,
      createdAt: now,
      updatedAt: now,
    };

    await setDoc(doc(db, COLLECTIONS.USERS, userId), userDoc);
  }

  static async getUser(userId: string): Promise<UserDocument | null> {
    const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, userId));
    return userDoc.exists() ? (userDoc.data() as UserDocument) : null;
  }

  static async updateUser(userId: string, updates: Partial<UserDocument>): Promise<void> {
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now(),
    };
    
    await updateDoc(doc(db, COLLECTIONS.USERS, userId), updateData);
  }

  static async updateUserLocation(
    userId: string, 
    location: {
      latitude: number;
      longitude: number;
      address?: string;
      city?: string;
      region?: string;
      country?: string;
    }
  ): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.USERS, userId), {
      location: {
        ...location,
        lastUpdated: Timestamp.now(),
      },
      updatedAt: Timestamp.now(),
    });
  }

  // Quiz operations
  static async getQuizzes(options?: {
    category?: string;
    difficulty?: string;
    isLocationBased?: boolean;
    limit?: number;
    lastDoc?: QueryDocumentSnapshot<DocumentData>;
  }): Promise<{ quizzes: QuizDocument[]; lastDoc?: QueryDocumentSnapshot<DocumentData> }> {
    let q = query(collection(db, COLLECTIONS.QUIZZES), where('isActive', '==', true));

    if (options?.category) {
      q = query(q, where('category', '==', options.category));
    }

    if (options?.difficulty) {
      q = query(q, where('difficulty', '==', options.difficulty));
    }

    if (options?.isLocationBased !== undefined) {
      q = query(q, where('isLocationBased', '==', options.isLocationBased));
    }

    q = query(q, orderBy('createdAt', 'desc'));

    if (options?.limit) {
      q = query(q, limit(options.limit));
    }

    if (options?.lastDoc) {
      q = query(q, startAfter(options.lastDoc));
    }

    const snapshot = await getDocs(q);
    const quizzes = snapshot.docs.map(doc => doc.data() as QuizDocument);
    const lastDoc = snapshot.docs[snapshot.docs.length - 1];

    return { quizzes, lastDoc };
  }

  static async getQuiz(quizId: string): Promise<QuizDocument | null> {
    const quizDoc = await getDoc(doc(db, COLLECTIONS.QUIZZES, quizId));
    return quizDoc.exists() ? (quizDoc.data() as QuizDocument) : null;
  }

  static async getLocationBasedQuizzes(
    city: string,
    region?: string,
    limitCount: number = 10
  ): Promise<QuizDocument[]> {
    let q = query(
      collection(db, COLLECTIONS.QUIZZES),
      where('isActive', '==', true),
      where('isLocationBased', '==', true),
      where('location.city', '==', city)
    );

    if (region) {
      q = query(q, where('location.region', '==', region));
    }

    q = query(q, orderBy('createdAt', 'desc'), limit(limitCount));

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => doc.data() as QuizDocument);
  }

  // Quiz attempt operations
  static async saveQuizAttempt(
    attempt: Omit<QuizAttemptDocument, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<string> {
    const now = Timestamp.now();
    const attemptDoc: Omit<QuizAttemptDocument, 'id'> = {
      ...attempt,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.QUIZ_ATTEMPTS), attemptDoc);
    
    // Update user stats
    await this.updateUserStats(attempt.userId, attempt.score, attempt.totalPoints);
    
    // Update quiz stats
    await this.updateQuizStats(attempt.quizId, attempt.score, attempt.totalPoints);

    return docRef.id;
  }

  static async getUserQuizAttempts(
    userId: string,
    limitCount: number = 20
  ): Promise<QuizAttemptDocument[]> {
    const q = query(
      collection(db, COLLECTIONS.QUIZ_ATTEMPTS),
      where('userId', '==', userId),
      orderBy('completedAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as QuizAttemptDocument));
  }

  // Daily challenge operations
  static async getDailyChallenge(date: string): Promise<DailyChallengeDocument | null> {
    const challengeDoc = await getDoc(doc(db, COLLECTIONS.DAILY_CHALLENGES, date));
    return challengeDoc.exists() ? (challengeDoc.data() as DailyChallengeDocument) : null;
  }

  static async getTodayChallenge(): Promise<DailyChallengeDocument | null> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    return this.getDailyChallenge(today);
  }

  // Real-time listeners
  static subscribeToUser(userId: string, callback: (user: UserDocument | null) => void): Unsubscribe {
    return onSnapshot(doc(db, COLLECTIONS.USERS, userId), (doc) => {
      callback(doc.exists() ? (doc.data() as UserDocument) : null);
    });
  }

  static subscribeToQuizzes(
    callback: (quizzes: QuizDocument[]) => void,
    options?: { category?: string; limit?: number }
  ): Unsubscribe {
    let q = query(collection(db, COLLECTIONS.QUIZZES), where('isActive', '==', true));

    if (options?.category) {
      q = query(q, where('category', '==', options.category));
    }

    q = query(q, orderBy('createdAt', 'desc'));

    if (options?.limit) {
      q = query(q, limit(options.limit));
    }

    return onSnapshot(q, (snapshot) => {
      const quizzes = snapshot.docs.map(doc => doc.data() as QuizDocument);
      callback(quizzes);
    });
  }

  // Helper methods
  private static async updateUserStats(
    userId: string,
    score: number,
    totalPoints: number
  ): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    const currentStats = user.stats || {
      quizzesCompleted: 0,
      totalScore: 0,
      averageScore: 0,
      streakDays: 0,
      lastActivity: Timestamp.now(),
    };

    const newQuizzesCompleted = currentStats.quizzesCompleted + 1;
    const newTotalScore = currentStats.totalScore + score;
    const newAverageScore = Math.round((newTotalScore / newQuizzesCompleted) * 100) / 100;

    await updateDoc(doc(db, COLLECTIONS.USERS, userId), {
      'stats.quizzesCompleted': newQuizzesCompleted,
      'stats.totalScore': newTotalScore,
      'stats.averageScore': newAverageScore,
      'stats.lastActivity': Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
  }

  private static async updateQuizStats(
    quizId: string,
    score: number,
    totalPoints: number
  ): Promise<void> {
    const quiz = await this.getQuiz(quizId);
    if (!quiz) return;

    const newTotalAttempts = quiz.totalAttempts + 1;
    const percentage = (score / totalPoints) * 100;
    const newAverageScore = Math.round(
      ((quiz.averageScore * quiz.totalAttempts) + percentage) / newTotalAttempts * 100
    ) / 100;

    await updateDoc(doc(db, COLLECTIONS.QUIZZES, quizId), {
      totalAttempts: newTotalAttempts,
      averageScore: newAverageScore,
      updatedAt: Timestamp.now(),
    });
  }

  // Utility method to convert Firebase Timestamp to JS Date
  static timestampToDate(timestamp: Timestamp): Date {
    return timestamp.toDate();
  }

  // Utility method to convert JS Date to Firebase Timestamp
  static dateToTimestamp(date: Date): Timestamp {
    return Timestamp.fromDate(date);
  }
}