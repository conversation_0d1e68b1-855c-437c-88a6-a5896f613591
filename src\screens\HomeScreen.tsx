import React, { useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator, 
  Alert, 
  ScrollView,
  Dimensions,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  FadeInDown, 
  FadeInRight, 
  SlideInLeft,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence
} from 'react-native-reanimated';
import { useTheme } from '../context/ThemeContext';
import { useAppSelector } from '../hooks/redux';
import { useLocation } from '../hooks/useLocation';
import { LocationService } from '../services/locationService';

const { width: screenWidth } = Dimensions.get('window');

export const HomeScreen: React.FC = () => {
  const { theme } = useTheme();
  const { currentUser } = useAppSelector((state) => state.user);
  const { location, isLocationLoading, locationError, refetchLocation } = useLocation();
  
  // Animation values
  const locationPulse = useSharedValue(1);
  const cardScale = useSharedValue(0.95);

  useEffect(() => {
    // Pulse animation for location icon when loading
    if (isLocationLoading) {
      locationPulse.value = withRepeat(
        withSequence(
          withSpring(1.2, { damping: 2 }),
          withSpring(1, { damping: 2 })
        ),
        -1,
        true
      );
    } else {
      locationPulse.value = withSpring(1);
    }

    // Entrance animation for cards
    cardScale.value = withSpring(1, { damping: 12 });
  }, [isLocationLoading]);

  const locationAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: locationPulse.value }],
  }));

  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: cardScale.value }],
  }));

  const handleLocationPress = () => {
    if (locationError) {
      Alert.alert(
        'Location Error',
        locationError,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: refetchLocation },
        ]
      );
    }
  };

  const getLocationDisplay = () => {
    if (isLocationLoading) {
      return 'Getting your location...';
    }
    
    if (locationError) {
      return 'Location unavailable (tap to retry)';
    }
    
    if (location) {
      return LocationService.formatLocationDisplay(location);
    }
    
    return 'Location not available';
  };

  const getLocationIcon = () => {
    if (isLocationLoading) {
      return 'location-outline';
    }
    
    if (locationError) {
      return 'location-outline';
    }
    
    return 'location';
  };

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flex: 1,
    },
    container: {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingBottom: 120, // Extra space for bottom navigation
      paddingTop: 10,
    },
    gradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: 200,
      opacity: 0.1,
    },
    header: {
      paddingTop: 10,
      paddingBottom: 30,
    },
    welcomeText: {
      fontSize: 24,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      marginBottom: 4,
      letterSpacing: 0.5,
    },
    nameText: {
      fontSize: 36,
      fontFamily: theme.fonts.secondaryBold,
      color: theme.colors.text,
      marginBottom: 8,
      lineHeight: 42,
    },
    greetingEmoji: {
      fontSize: 32,
      marginLeft: 8,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.card,
      padding: 20,
      borderRadius: 20,
      marginBottom: 32,
      ...Platform.select({
        ios: {
          shadowColor: theme.colors.shadow,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
        },
        android: {
          elevation: 6,
        },
        web: {
          boxShadow: `0 4px 20px ${theme.colors.shadow}15`,
        },
      }),
    },
    locationIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: `${theme.colors.primary}15`,
      justifyContent: 'center',
      alignItems: 'center',
    },
    locationContent: {
      flex: 1,
      marginLeft: 16,
    },
    locationLabel: {
      fontSize: 13,
      fontFamily: theme.fonts.primaryMedium,
      color: theme.colors.textSecondary,
      marginBottom: 4,
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
    locationText: {
      fontSize: 16,
      fontFamily: theme.fonts.primary,
      color: theme.colors.text,
      lineHeight: 22,
      flexWrap: 'wrap',
    },
    locationAccuracy: {
      fontSize: 11,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      marginTop: 6,
      opacity: 0.8,
    },
    refreshButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: `${theme.colors.primary}10`,
    },
    sectionTitle: {
      fontSize: 22,
      fontFamily: theme.fonts.secondaryBold,
      color: theme.colors.text,
      marginBottom: 20,
      marginLeft: 4,
    },
    cardsContainer: {
      gap: 16,
    },
    actionCard: {
      backgroundColor: theme.colors.card,
      padding: 24,
      borderRadius: 20,
      marginBottom: 4,
      ...Platform.select({
        ios: {
          shadowColor: theme.colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.08,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
        web: {
          boxShadow: `0 2px 16px ${theme.colors.shadow}10`,
        },
      }),
    },
    cardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    cardIcon: {
      fontSize: 28,
      marginRight: 12,
    },
    cardTitleContainer: {
      flex: 1,
    },
    actionTitle: {
      fontSize: 18,
      fontFamily: theme.fonts.primaryBold,
      color: theme.colors.text,
      lineHeight: 24,
    },
    actionSubtitle: {
      fontSize: 13,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      marginTop: 2,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    actionDescription: {
      fontSize: 15,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      lineHeight: 22,
      marginTop: 4,
    },
    cardAction: {
      alignSelf: 'flex-end',
      marginTop: 12,
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: `${theme.colors.primary}15`,
      borderRadius: 12,
    },
    cardActionText: {
      fontSize: 13,
      fontFamily: theme.fonts.primaryMedium,
      color: theme.colors.primary,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    specialCard: {
      backgroundColor: `${theme.colors.primary}08`,
      borderWidth: 1,
      borderColor: `${theme.colors.primary}20`,
    },
    featuredBadge: {
      position: 'absolute',
      top: 16,
      right: 16,
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 8,
    },
    featuredText: {
      fontSize: 10,
      fontFamily: theme.fonts.primaryBold,
      color: theme.colors.background,
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
  });

  const getGreetingEmoji = () => {
    const hour = new Date().getHours();
    if (hour < 12) return '☀️';
    if (hour < 17) return '🌤️';
    return '🌙';
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.container}
        showsVerticalScrollIndicator={false}
        bounces={true}
      >
        {/* Header Section */}
        <Animated.View style={styles.header} entering={FadeInDown.delay(100).springify()}>
          <Text style={styles.welcomeText}>Good {new Date().getHours() < 12 ? 'Morning' : new Date().getHours() < 17 ? 'Afternoon' : 'Evening'}</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.nameText}>{currentUser?.name || 'User'}</Text>
            <Text style={styles.greetingEmoji}>{getGreetingEmoji()}</Text>
          </View>
        </Animated.View>

        {/* Location Section */}
        <Animated.View entering={FadeInDown.delay(200).springify()}>
          <TouchableOpacity 
            style={styles.locationContainer} 
            onPress={handleLocationPress}
            disabled={!locationError}
            activeOpacity={0.7}
          >
            <Animated.View style={[styles.locationIconContainer, locationAnimatedStyle]}>
              {isLocationLoading ? (
                <ActivityIndicator size="small" color={theme.colors.primary} />
              ) : (
                <Ionicons 
                  name={getLocationIcon()} 
                  size={24} 
                  color={locationError ? theme.colors.error : theme.colors.primary} 
                />
              )}
            </Animated.View>
            
            <View style={styles.locationContent}>
              <Text style={styles.locationLabel}>Current Location</Text>
              <Text style={styles.locationText}>{getLocationDisplay()}</Text>
              {location && (
                <Text style={styles.locationAccuracy}>
                  {LocationService.getLocationAccuracy(location)}
                </Text>
              )}
            </View>
            
            {locationError && (
              <TouchableOpacity style={styles.refreshButton}>
                <Ionicons name="refresh" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        </Animated.View>

        {/* Main Content */}
        <Animated.View entering={FadeInDown.delay(300).springify()}>
          <Text style={styles.sectionTitle}>What would you like to do?</Text>
          
          <View style={styles.cardsContainer}>
            {/* Featured Daily Challenge */}
            <Animated.View entering={SlideInLeft.delay(400).springify()}>
              <TouchableOpacity 
                style={[styles.actionCard, styles.specialCard]}
                activeOpacity={0.8}
              >
                <View style={styles.featuredBadge}>
                  <Text style={styles.featuredText}>Featured</Text>
                </View>
                
                <View style={styles.cardHeader}>
                  <Text style={styles.cardIcon}>🎯</Text>
                  <View style={styles.cardTitleContainer}>
                    <Text style={styles.actionTitle}>Daily Challenge</Text>
                    <Text style={styles.actionSubtitle}>Special Quest</Text>
                  </View>
                </View>
                
                <Text style={styles.actionDescription}>
                  Complete today's special challenge and earn bonus points! New challenges unlock every 24 hours.
                </Text>
                
                <View style={styles.cardAction}>
                  <Text style={styles.cardActionText}>Start Now</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            {/* Quick Quiz */}
            <Animated.View entering={SlideInLeft.delay(500).springify()}>
              <TouchableOpacity style={styles.actionCard} activeOpacity={0.8}>
                <View style={styles.cardHeader}>
                  <Text style={styles.cardIcon}>📚</Text>
                  <View style={styles.cardTitleContainer}>
                    <Text style={styles.actionTitle}>Quick Quiz</Text>
                    <Text style={styles.actionSubtitle}>Random Topics</Text>
                  </View>
                  <Ionicons name="play-circle-outline" size={24} color={theme.colors.primary} />
                </View>
                
                <Text style={styles.actionDescription}>
                  Jump into a random quiz and test your knowledge on various topics. Perfect for quick learning sessions.
                </Text>
                
                <View style={styles.cardAction}>
                  <Text style={styles.cardActionText}>Play</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            {/* Local Quizzes */}
            <Animated.View entering={SlideInLeft.delay(600).springify()}>
              <TouchableOpacity style={styles.actionCard} activeOpacity={0.8}>
                <View style={styles.cardHeader}>
                  <Text style={styles.cardIcon}>📍</Text>
                  <View style={styles.cardTitleContainer}>
                    <Text style={styles.actionTitle}>Local Quizzes</Text>
                    <Text style={styles.actionSubtitle}>
                      {location?.city ? `${location.city} Area` : 'Location Based'}
                    </Text>
                  </View>
                  <Ionicons name="location-outline" size={20} color={theme.colors.primary} />
                </View>
                
                <Text style={styles.actionDescription}>
                  {location 
                    ? `Discover quizzes related to ${location.city || 'your area'} and local knowledge.`
                    : 'Enable location to see area-specific quizzes and local content.'
                  }
                </Text>
                
                <View style={styles.cardAction}>
                  <Text style={styles.cardActionText}>Explore</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>

            {/* Practice Mode */}
            <Animated.View entering={SlideInLeft.delay(700).springify()}>
              <TouchableOpacity style={styles.actionCard} activeOpacity={0.8}>
                <View style={styles.cardHeader}>
                  <Text style={styles.cardIcon}>🎓</Text>
                  <View style={styles.cardTitleContainer}>
                    <Text style={styles.actionTitle}>Practice Mode</Text>
                    <Text style={styles.actionSubtitle}>Skill Building</Text>
                  </View>
                  <Ionicons name="school-outline" size={20} color={theme.colors.primary} />
                </View>
                
                <Text style={styles.actionDescription}>
                  Practice with custom difficulty levels and track your progress across different subjects.
                </Text>
                
                <View style={styles.cardAction}>
                  <Text style={styles.cardActionText}>Practice</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};