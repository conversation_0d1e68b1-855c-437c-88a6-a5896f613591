import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User, UserState, UserLocation } from '../../types/user';

const initialState: UserState = {
  currentUser: {
    userID: "12392-1232-123142",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phNo: "7377077604",
    role: "admin"
  },
  isLoggedIn: true,
  isLocationLoading: false,
  locationError: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    loginUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.isLoggedIn = true;
    },
    logoutUser: (state) => {
      state.currentUser = null;
      state.isLoggedIn = false;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload };
      }
    },
    setLocationLoading: (state, action: PayloadAction<boolean>) => {
      state.isLocationLoading = action.payload;
    },
    setLocationError: (state, action: PayloadAction<string | null>) => {
      state.locationError = action.payload;
    },
    updateUserLocation: (state, action: PayloadAction<UserLocation>) => {
      if (state.currentUser) {
        state.currentUser.location = action.payload;
      }
      state.isLocationLoading = false;
      state.locationError = null;
    },
  },
});

export const { 
  loginUser, 
  logoutUser, 
  updateUser, 
  setLocationLoading, 
  setLocationError, 
  updateUserLocation 
} = userSlice.actions;
export default userSlice.reducer;