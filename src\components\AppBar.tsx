import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAppSelector } from '../hooks/redux';
import ProfileModal from './ProfileModal';

interface AppBarProps {
  title?: string;
}

export const AppBar: React.FC<AppBarProps> = ({ title = 'MCQs' }) => {
  const { theme, toggleTheme, isDark } = useTheme();
  const { currentUser } = useAppSelector((state) => state.user);
  const [isProfileModalVisible, setProfileModalVisible] = useState(false);

  const styles = StyleSheet.create({
    safeArea: {
      backgroundColor: theme.colors.primary,
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: Platform.OS === 'web' ? 16 : 12,
      backgroundColor: theme.colors.primary,
      ...(Platform.OS === 'web' && {
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }),
    },
    title: {
      fontSize: 20,
      fontFamily: theme.fonts.secondary,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    rightSection: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    profileButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.primaryLight,
      justifyContent: 'center',
      alignItems: 'center',
    },
    themeButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: theme.colors.primaryLight,
    },
    profileText: {
      color: theme.colors.text,
      fontSize: 16,
      fontFamily: theme.fonts.primaryBold,
    },
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <>
      <SafeAreaView style={styles.safeArea} edges={['top']}>
        <View style={styles.container}>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.rightSection}>
            <TouchableOpacity
              style={styles.themeButton}
              onPress={toggleTheme}
            >
              <Ionicons
                name={isDark ? 'sunny' : 'moon'}
                size={20}
                color={theme.colors.text}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.profileButton}
              onPress={() => setProfileModalVisible(true)}
            >
              <Text style={styles.profileText}>
                {currentUser ? getInitials(currentUser.name) : 'U'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
      
      <ProfileModal
        visible={isProfileModalVisible}
        onClose={() => setProfileModalVisible(false)}
      />
    </>
  );
};