import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { Platform } from 'react-native';

const firebaseConfig = {
  apiKey: "AIzaSyC7ORvxHYEFDANAu9__f7g4tROTA-dHd7Q",
  authDomain: "my-city-quest-653d8.firebaseapp.com",
  projectId: "my-city-quest-653d8",
  storageBucket: "my-city-quest-653d8.firebasestorage.app",
  messagingSenderId: "932762087438",
  appId: "1:932762087438:web:2f6d30b8920d0c367ecc5f",
  measurementId: "G-RYF6FM7ZSS"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Auth
export const auth = getAuth(app);

// For development, you can connect to Firestore emulator
// Uncomment the following lines if you want to use local emulator
// if (__DEV__ && Platform.OS === 'web') {
//   try {
//     connectFirestoreEmulator(db, 'localhost', 8080);
//   } catch (error) {
//     console.log('Firestore emulator connection error:', error);
//   }
// }

export default app;