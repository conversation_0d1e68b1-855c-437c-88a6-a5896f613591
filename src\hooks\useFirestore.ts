import { useState, useEffect } from 'react';
import { Unsubscribe } from 'firebase/firestore';
import { FirestoreService } from '../services/firestoreService';
import { UserDocument, QuizDocument, QuizAttemptDocument } from '../types/firestore';

// Hook for real-time user data
export const useUser = (userId: string | null) => {
  const [user, setUser] = useState<UserDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setUser(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const unsubscribe: Unsubscribe = FirestoreService.subscribeToUser(userId, (userData) => {
      setUser(userData);
      setLoading(false);
      if (!userData) {
        setError('User not found');
      }
    });

    return () => unsubscribe();
  }, [userId]);

  return { user, loading, error };
};

// Hook for real-time quizzes
export const useQuizzes = (options?: { category?: string; limit?: number }) => {
  const [quizzes, setQuizzes] = useState<QuizDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);

    const unsubscribe: Unsubscribe = FirestoreService.subscribeToQuizzes(
      (quizzesData) => {
        setQuizzes(quizzesData);
        setLoading(false);
      },
      options
    );

    return () => unsubscribe();
  }, [options?.category, options?.limit]);

  return { quizzes, loading, error };
};

// Hook for location-based quizzes
export const useLocationQuizzes = (city?: string, region?: string) => {
  const [quizzes, setQuizzes] = useState<QuizDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!city) {
      setQuizzes([]);
      return;
    }

    const fetchLocationQuizzes = async () => {
      setLoading(true);
      setError(null);

      try {
        const locationQuizzes = await FirestoreService.getLocationBasedQuizzes(city, region, 10);
        setQuizzes(locationQuizzes);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch location quizzes');
      } finally {
        setLoading(false);
      }
    };

    fetchLocationQuizzes();
  }, [city, region]);

  return { quizzes, loading, error };
};

// Hook for user quiz attempts
export const useUserAttempts = (userId: string | null) => {
  const [attempts, setAttempts] = useState<QuizAttemptDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setAttempts([]);
      return;
    }

    const fetchAttempts = async () => {
      setLoading(true);
      setError(null);

      try {
        const userAttempts = await FirestoreService.getUserQuizAttempts(userId, 20);
        setAttempts(userAttempts);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch quiz attempts');
      } finally {
        setLoading(false);
      }
    };

    fetchAttempts();
  }, [userId]);

  return { attempts, loading, error, refetch: () => {
    if (userId) {
      setAttempts([]);
      const fetchAttempts = async () => {
        setLoading(true);
        try {
          const userAttempts = await FirestoreService.getUserQuizAttempts(userId, 20);
          setAttempts(userAttempts);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to fetch quiz attempts');
        } finally {
          setLoading(false);
        }
      };
      fetchAttempts();
    }
  }};
};

// Hook for daily challenge
export const useDailyChallenge = () => {
  const [challenge, setChallenge] = useState<{ quiz: QuizDocument; challenge: any } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDailyChallenge = async () => {
      setLoading(true);
      setError(null);

      try {
        const dailyChallenge = await FirestoreService.getTodayChallenge();
        if (dailyChallenge) {
          const quiz = await FirestoreService.getQuiz(dailyChallenge.quizId);
          if (quiz) {
            setChallenge({ quiz, challenge: dailyChallenge });
          } else {
            setError('Daily challenge quiz not found');
          }
        } else {
          setChallenge(null);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch daily challenge');
      } finally {
        setLoading(false);
      }
    };

    fetchDailyChallenge();
  }, []);

  return { challenge, loading, error };
};