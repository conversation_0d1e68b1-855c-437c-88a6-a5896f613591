import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../context/ThemeContext';

export const ExploreScreen: React.FC = () => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontFamily: theme.fonts.secondaryBold,
      color: theme.colors.text,
      marginBottom: 10,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    exploreCard: {
      backgroundColor: theme.colors.card,
      padding: 20,
      borderRadius: 12,
      marginTop: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Explore</Text>
      <Text style={styles.subtitle}>Discover new topics and categories</Text>
      
      <View style={styles.exploreCard}>
        <Text style={[styles.subtitle, { color: theme.colors.text }]}>
          Browse through various subjects and find interesting quizzes to challenge yourself!
        </Text>
      </View>
    </View>
  );
};