export interface ThemeColors {
  primary: string;
  primaryDark: string;
  primaryLight: string;
  secondary: string;
  background: string;
  surface: string;
  card: string;
  text: string;
  textSecondary: string;
  border: string;
  notification: string;
  success: string;
  warning: string;
  error: string;
  accent: string;
  shadow: string;
}

export interface ThemeFonts {
  primary: string;           // Main UI font (Montserrat)
  secondary: string;         // Decorative font (Playfair Display)
  primaryBold: string;       // Bold version of primary
  primaryMedium: string;     // Medium weight of primary
  primaryLight: string;      // Light weight of primary
  secondaryBold: string;     // Bold version of secondary
  monospace: string;         // Monospace font for code/numbers
}

export interface Theme {
  colors: ThemeColors;
  fonts: ThemeFonts;
  dark: boolean;
}

export type ThemeMode = 'light' | 'dark';

export interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  isDark: boolean;
}