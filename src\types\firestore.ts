import { Timestamp } from 'firebase/firestore';

// Base document interface
export interface FirestoreDocument {
  id: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// User document in Firestore
export interface UserDocument extends FirestoreDocument {
  name: string;
  email: string;
  phoneNumber?: string;
  role: 'user' | 'admin' | 'moderator';
  avatar?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    region?: string;
    country?: string;
    lastUpdated: Timestamp;
  };
  preferences?: {
    notifications: boolean;
    darkMode: boolean;
    language: string;
  };
  stats?: {
    quizzesCompleted: number;
    totalScore: number;
    averageScore: number;
    streakDays: number;
    lastActivity: Timestamp;
  };
}

// Quiz document structure
export interface QuizDocument extends FirestoreDocument {
  title: string;
  description: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questions: QuizQuestion[];
  isActive: boolean;
  isLocationBased: boolean;
  location?: {
    city: string;
    region: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  tags: string[];
  createdBy: string; // User ID
  totalAttempts: number;
  averageScore: number;
}

// Quiz question structure
export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number; // Index of correct option
  explanation?: string;
  points: number;
  timeLimit?: number; // in seconds
}

// Quiz attempt/result document
export interface QuizAttemptDocument extends FirestoreDocument {
  userId: string;
  quizId: string;
  answers: QuizAnswer[];
  score: number;
  totalPoints: number;
  percentage: number;
  timeSpent: number; // in seconds
  completedAt: Timestamp;
  location?: {
    latitude: number;
    longitude: number;
    city?: string;
  };
}

// Individual answer in quiz attempt
export interface QuizAnswer {
  questionId: string;
  selectedAnswer: number;
  isCorrect: boolean;
  points: number;
  timeSpent: number;
}

// Daily challenge document
export interface DailyChallengeDocument extends FirestoreDocument {
  date: string; // YYYY-MM-DD format
  quizId: string;
  isActive: boolean;
  bonusMultiplier: number;
  participants: number;
  averageScore: number;
}

// Leaderboard entry
export interface LeaderboardEntry {
  userId: string;
  userName: string;
  userAvatar?: string;
  score: number;
  rank: number;
  location?: {
    city: string;
    region: string;
  };
  lastUpdated: Timestamp;
}

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  QUIZZES: 'quizzes',
  QUIZ_ATTEMPTS: 'quizAttempts',
  DAILY_CHALLENGES: 'dailyChallenges',
  LEADERBOARDS: 'leaderboards',
} as const;

// Subcollection names
export const SUBCOLLECTIONS = {
  USER_ATTEMPTS: 'attempts',
  USER_FAVORITES: 'favorites',
  QUIZ_RATINGS: 'ratings',
} as const;