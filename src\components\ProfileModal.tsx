import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { logoutUser } from '../store/slices/userSlice';

interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
}

const ProfileModal: React.FC<ProfileModalProps> = ({ visible, onClose }) => {
  const { theme } = useTheme();
  const { currentUser } = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            dispatch(logoutUser());
            onClose();
          },
        },
      ]
    );
  };

  const handleManageProfile = () => {
    // TODO: Navigate to manage profile screen
    Alert.alert('Info', 'Manage Profile feature coming soon!');
    onClose();
  };

  const handleViewProfile = () => {
    // TODO: Navigate to view profile screen
    Alert.alert('Info', 'View Profile feature coming soon!');
    onClose();
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 24,
      margin: 20,
      width: '90%',
      maxWidth: 400,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 20,
      fontFamily: theme.fonts.secondaryBold,
      color: theme.colors.text,
    },
    closeButton: {
      padding: 4,
    },
    profileSection: {
      alignItems: 'center',
      marginBottom: 24,
    },
    avatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    avatarText: {
      fontSize: 32,
      fontFamily: theme.fonts.primaryBold,
      color: theme.colors.text,
    },
    userName: {
      fontSize: 18,
      fontFamily: theme.fonts.primaryBold,
      color: theme.colors.text,
      marginBottom: 4,
    },
    userRole: {
      fontSize: 14,
      fontFamily: theme.fonts.primaryMedium,
      color: theme.colors.textSecondary,
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 12,
      paddingVertical: 4,
      borderRadius: 12,
      textTransform: 'uppercase',
    },
    userDetails: {
      marginBottom: 24,
    },
    detailRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    detailIcon: {
      marginRight: 12,
      width: 20,
    },
    detailLabel: {
      fontSize: 14,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      flex: 1,
    },
    detailValue: {
      fontSize: 14,
      fontFamily: theme.fonts.primaryMedium,
      color: theme.colors.text,
    },
    actions: {
      gap: 12,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      backgroundColor: theme.colors.card,
    },
    actionButtonText: {
      fontSize: 16,
      fontFamily: theme.fonts.primaryMedium,
      color: theme.colors.text,
      marginLeft: 12,
    },
    logoutButton: {
      backgroundColor: theme.colors.error,
    },
    logoutButtonText: {
      color: '#FFFFFF',
    },
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!currentUser) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity style={styles.overlay} activeOpacity={1} onPress={onClose}>
        <TouchableOpacity activeOpacity={1} onPress={() => {}}>
          <View style={styles.modalContainer}>
            <View style={styles.header}>
              <Text style={styles.title}>Profile</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.profileSection}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {getInitials(currentUser.name)}
                </Text>
              </View>
              <Text style={styles.userName}>{currentUser.name}</Text>
              <Text style={styles.userRole}>{currentUser.role}</Text>
            </View>

            <View style={styles.userDetails}>
              <View style={styles.detailRow}>
                <Ionicons
                  name="mail-outline"
                  size={20}
                  color={theme.colors.textSecondary}
                  style={styles.detailIcon}
                />
                <Text style={styles.detailLabel}>Email</Text>
                <Text style={styles.detailValue}>{currentUser.email}</Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons
                  name="call-outline"
                  size={20}
                  color={theme.colors.textSecondary}
                  style={styles.detailIcon}
                />
                <Text style={styles.detailLabel}>Phone</Text>
                <Text style={styles.detailValue}>{currentUser.phNo}</Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons
                  name="id-card-outline"
                  size={20}
                  color={theme.colors.textSecondary}
                  style={styles.detailIcon}
                />
                <Text style={styles.detailLabel}>User ID</Text>
                <Text style={styles.detailValue}>{currentUser.userID}</Text>
              </View>
            </View>

            <View style={styles.actions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleViewProfile}
              >
                <Ionicons name="person-outline" size={20} color={theme.colors.text} />
                <Text style={styles.actionButtonText}>View Profile</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleManageProfile}
              >
                <Ionicons name="settings-outline" size={20} color={theme.colors.text} />
                <Text style={styles.actionButtonText}>Manage Profile</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, styles.logoutButton]}
                onPress={handleLogout}
              >
                <Ionicons name="log-out-outline" size={20} color="#FFFFFF" />
                <Text style={[styles.actionButtonText, styles.logoutButtonText]}>
                  Logout
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

export default ProfileModal;