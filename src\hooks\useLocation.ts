import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './redux';
import { setLocationLoading, setLocationError, updateUserLocation } from '../store/slices/userSlice';
import { LocationService } from '../services/locationService';

export const useLocation = () => {
  const dispatch = useAppDispatch();
  const { isLocationLoading, locationError, currentUser } = useAppSelector(
    (state) => state.user
  );

  const fetchLocation = async () => {
    try {
      dispatch(setLocationLoading(true));
      dispatch(setLocationError(null));

      const location = await LocationService.getCurrentLocation();
      dispatch(updateUserLocation(location));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get location';
      dispatch(setLocationError(errorMessage));
      console.error('Location fetch error:', error);
    }
  };

  const refetchLocation = () => {
    fetchLocation();
  };

  // Auto-fetch location on hook initialization
  useEffect(() => {
    if (!currentUser?.location) {
      fetchLocation();
    }
  }, []);

  return {
    location: currentUser?.location,
    isLocationLoading,
    locationError,
    refetchLocation,
  };
};