import * as Location from 'expo-location';
import { Platform } from 'react-native';
import { UserLocation } from '../types/user';

export class LocationService {
  static async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // For web, use browser geolocation API permissions
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        return permission.state === 'granted' || permission.state === 'prompt';
      }

      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  }

  static async getCurrentLocation(): Promise<UserLocation> {
    try {
      // Request permissions first
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('Location permission denied');
      }

      if (Platform.OS === 'web') {
        // Use browser geolocation API for web
        return new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(
            async (position) => {
              const { latitude, longitude } = position.coords;
              try {
                const address = await this.reverseGeocode(latitude, longitude);
                resolve({
                  latitude,
                  longitude,
                  ...address,
                  timestamp: Date.now(),
                });
              } catch (error) {
                resolve({
                  latitude,
                  longitude,
                  timestamp: Date.now(),
                });
              }
            },
            (error) => reject(new Error(`Geolocation error: ${error.message}`)),
            {
              enableHighAccuracy: true,
              timeout: 15000,
              maximumAge: 10000,
            }
          );
        });
      }

      // For mobile platforms, use Expo Location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 1000,
        distanceInterval: 1,
      });

      const { latitude, longitude } = location.coords;

      // Get address information
      try {
        const address = await this.reverseGeocode(latitude, longitude);
        return {
          latitude,
          longitude,
          ...address,
          timestamp: Date.now(),
        };
      } catch (error) {
        console.warn('Failed to get address information:', error);
        return {
          latitude,
          longitude,
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      throw error;
    }
  }

  static async reverseGeocode(latitude: number, longitude: number) {
    try {
      if (Platform.OS === 'web') {
        // For web, we'll skip reverse geocoding to avoid API key requirements
        return {};
      }

      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (result && result.length > 0) {
        const location = result[0];
        return {
          address: [location.streetNumber, location.street].filter(Boolean).join(' '),
          city: location.city || location.subregion,
          region: location.region,
          country: location.country,
          postalCode: location.postalCode,
        };
      }

      return {};
    } catch (error) {
      console.warn('Reverse geocoding failed:', error);
      return {};
    }
  }

  static formatLocationDisplay(location: UserLocation): string {
    const parts = [];
    
    if (location.city) {
      parts.push(location.city);
    }
    
    if (location.region && location.region !== location.city) {
      parts.push(location.region);
    }
    
    if (location.country) {
      parts.push(location.country);
    }

    if (parts.length === 0) {
      return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
    }

    return parts.join(', ');
  }

  static getLocationAccuracy(location: UserLocation): string {
    const now = Date.now();
    const ageMinutes = Math.floor((now - location.timestamp) / (1000 * 60));
    
    if (ageMinutes === 0) {
      return 'Current location';
    } else if (ageMinutes === 1) {
      return '1 minute ago';
    } else if (ageMinutes < 60) {
      return `${ageMinutes} minutes ago`;
    } else {
      const ageHours = Math.floor(ageMinutes / 60);
      return ageHours === 1 ? '1 hour ago' : `${ageHours} hours ago`;
    }
  }
}