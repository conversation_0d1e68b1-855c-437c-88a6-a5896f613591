import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../context/ThemeContext';

export const QuestsScreen: React.FC = () => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontFamily: theme.fonts.secondaryBold,
      color: theme.colors.text,
      marginBottom: 10,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: theme.fonts.primary,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    questCard: {
      backgroundColor: theme.colors.card,
      padding: 20,
      borderRadius: 12,
      marginTop: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    questText: {
      color: theme.colors.text,
      fontSize: 16,
      fontFamily: theme.fonts.primary,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Quests</Text>
      <Text style={styles.subtitle}>Complete challenges and earn rewards</Text>
      
      <View style={styles.questCard}>
        <Text style={styles.questText}>
          Take on exciting quests and unlock achievements as you progress through your learning journey!
        </Text>
      </View>
    </View>
  );
};