{"expo": {"name": "MCQs", "slug": "MCQs", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location to show your current area and provide location-based quiz content.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to show your current area and provide location-based quiz content."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "webpack"}, "plugins": ["expo-font", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow MCQs to use your location to show your current area and provide location-based quiz content."}]]}}