import { ThemeColors } from '../types/theme';

export const darkTheme: ThemeColors = {
  primary: '#2D5A27',        // Rich dark green
  primaryDark: '#1F3F1A',    // Deeper forest green
  primaryLight: '#4A8540',   // Lighter forest green
  secondary: '#D4AF37',      // Gold accent
  background: '#0F1419',     // Very dark background
  surface: '#1A1F2E',       // Dark surface
  card: '#242B3D',          // Card background
  text: '#E8F4F8',          // Light text
  textSecondary: '#A8B2C8', // Secondary text
  border: '#2D3748',        // Border color
  notification: '#D4AF37',  // Notification gold
  success: '#4CAF50',       // Success green
  warning: '#FF9800',       // Warning orange
  error: '#F44336',         // Error red
  accent: '#8BC34A',        // Light green accent
  shadow: '#000000',        // Shadow
};

export const lightTheme: ThemeColors = {
  primary: '#2E7D32',        // Emerald green
  primaryDark: '#1B5E20',    // Dark emerald
  primaryLight: '#4CAF50',   // Light emerald
  secondary: '#FF6F00',      // Warm orange
  background: '#FAFFFE',     // Off-white background
  surface: '#FFFFFF',       // Pure white surface
  card: '#F8FBF8',          // Light green tint card
  text: '#1A1A1A',          // Dark text
  textSecondary: '#666666', // Gray text
  border: '#E0E0E0',        // Light border
  notification: '#FF6F00',  // Orange notification
  success: '#4CAF50',       // Success green
  warning: '#FF9800',       // Warning orange
  error: '#F44336',         // Error red
  accent: '#81C784',        // Soft green accent
  shadow: '#000000',        // Shadow
};