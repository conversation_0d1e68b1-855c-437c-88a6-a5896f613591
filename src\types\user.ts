export interface UserLocation {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  region?: string;
  country?: string;
  postalCode?: string;
  timestamp: number;
}

export interface User {
  userID: string;
  name: string;
  email: string;
  phNo: string;
  role: string;
  location?: UserLocation;
}

export interface UserState {
  currentUser: User | null;
  isLoggedIn: boolean;
  isLocationLoading: boolean;
  locationError: string | null;
}