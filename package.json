{"name": "mcqs", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/montserrat": "^0.4.1", "@expo-google-fonts/playfair-display": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@expo/webpack-config": "^19.0.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.17", "expo-font": "~13.3.2", "expo-location": "~18.1.6", "expo-status-bar": "~2.2.3", "firebase": "^11.10.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-web": "^0.20.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}