import React from 'react';
import { View, StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { store } from './src/store';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { AppBar } from './src/components/AppBar';
import { BottomTabNavigator } from './src/navigation/BottomTabNavigator';
import { useAppSelector } from './src/hooks/redux';
import { useLocation } from './src/hooks/useLocation';

function AppContent() {
  const { theme, isDark } = useTheme();
  const { isLoggedIn } = useAppSelector((state) => state.user);
  
  // Initialize location tracking
  useLocation();

  if (!isLoggedIn) {
    // TODO: Add login screen
    return null;
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
  });

  return (
    <NavigationContainer>
      <View style={styles.container}>
        <AppBar />
        <BottomTabNavigator />
        <StatusBar style={isDark ? 'light' : 'dark'} />
      </View>
    </NavigationContainer>
  );
}

export default function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}
